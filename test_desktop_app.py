#!/usr/bin/env python3
"""
Test script for the desktop application
Verifies that all components can be imported and basic functionality works
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import customtkinter as ctk
        print("✓ CustomTkinter imported successfully")
    except ImportError as e:
        print(f"✗ CustomTkinter import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ Pandas imported successfully")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    try:
        import qrcode
        print("✓ QRCode imported successfully")
    except ImportError as e:
        print(f"✗ QRCode import failed: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk, ImageDraw, ImageFont
        print("✓ Pillow imported successfully")
    except ImportError as e:
        print(f"✗ Pillow import failed: {e}")
        return False
    
    try:
        from cryptography.fernet import Fernet
        print("✓ Cryptography imported successfully")
    except ImportError as e:
        print(f"✗ Cryptography import failed: {e}")
        return False
    
    try:
        import psutil
        print("✓ Psutil imported successfully")
    except ImportError as e:
        print(f"✗ Psutil import failed: {e}")
        return False
    
    return True

def test_application_import():
    """Test that the main application can be imported"""
    print("\nTesting application import...")
    
    try:
        from desktop_app import QRIDDesktopApp
        print("✓ Desktop application imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Desktop application import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Desktop application import error: {e}")
        return False

def test_directory_creation():
    """Test that directories can be created"""
    print("\nTesting directory creation...")
    
    try:
        base_dir = Path(__file__).parent
        test_dirs = [
            base_dir / "test_data",
            base_dir / "test_data" / "qr_codes",
            base_dir / "test_data" / "participant_list",
            base_dir / "test_data" / "id_templates"
        ]
        
        for directory in test_dirs:
            directory.mkdir(parents=True, exist_ok=True)
        
        print("✓ Directories created successfully")
        
        # Clean up test directories
        import shutil
        shutil.rmtree(base_dir / "test_data")
        print("✓ Test directories cleaned up")
        
        return True
    except Exception as e:
        print(f"✗ Directory creation failed: {e}")
        return False

def test_encryption():
    """Test encryption functionality"""
    print("\nTesting encryption...")
    
    try:
        from cryptography.fernet import Fernet
        
        # Generate a test key
        key = Fernet.generate_key()
        fernet = Fernet(key)
        
        # Test encryption/decryption
        test_data = "id=001;name=Test User;position=Developer;company=Test Corp"
        encrypted = fernet.encrypt(test_data.encode())
        decrypted = fernet.decrypt(encrypted).decode()
        
        if test_data == decrypted:
            print("✓ Encryption/decryption working correctly")
            return True
        else:
            print("✗ Encryption/decryption mismatch")
            return False
            
    except Exception as e:
        print(f"✗ Encryption test failed: {e}")
        return False

def test_qr_generation():
    """Test QR code generation"""
    print("\nTesting QR code generation...")
    
    try:
        import qrcode
        from PIL import Image
        import tempfile
        
        # Generate a test QR code
        test_data = "Test QR Code Data"
        qr_img = qrcode.make(test_data)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
            qr_img.save(tmp_file.name)
            
            # Verify file was created and can be opened
            with Image.open(tmp_file.name) as img:
                if img.size[0] > 0 and img.size[1] > 0:
                    print("✓ QR code generation working correctly")
                    os.unlink(tmp_file.name)  # Clean up
                    return True
                else:
                    print("✗ QR code generation produced invalid image")
                    return False
                    
    except Exception as e:
        print(f"✗ QR code generation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Smart QR ID Scanner - Desktop Application Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_application_import,
        test_directory_creation,
        test_encryption,
        test_qr_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The application should work correctly.")
        return True
    else:
        print("✗ Some tests failed. Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
