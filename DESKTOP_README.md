# Smart QR ID Scanner - Desktop Application

A local desktop application for QR code ID management and printing. Works offline except for email functionality.

## Features

- **Local Operation**: Works completely offline except for email sending
- **Modern UI**: Clean, professional interface using CustomTkinter
- **Dataset Management**: Upload and manage CSV employee datasets
- **Template Management**: Upload and manage ID card templates
- **QR Code Generation**: Generate QR codes with encryption
- **ID Card Preview**: Preview ID cards with QR codes overlaid on templates
- **Email Integration**: Send QR codes via email (requires internet)
- **Batch Processing**: Generate multiple QR codes and export all ID cards
- **Memory Optimized**: Efficient memory usage for large datasets

## Requirements

- Python 3.7 or higher
- Windows, macOS, or Linux
- Internet connection only required for email functionality

## Installation

### Option 1: Quick Start (Recommended)

1. Download all files to a folder
2. Double-click `run_desktop_app.py` or run:
   ```bash
   python run_desktop_app.py
   ```
3. The launcher will automatically install dependencies and start the application

### Option 2: Manual Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements_desktop.txt
   ```

2. Run the application:
   ```bash
   python desktop_app.py
   ```

## Usage Guide

### 1. First Time Setup

1. **Upload Dataset**: 
   - Go to "Dataset" section
   - Click "Select CSV File" and choose your employee data file
   - Required columns: ID, Name, Position, Company
   - Optional column: Email (for sending QR codes)
   - Click "Upload Dataset"

2. **Upload Template**:
   - Go to "Templates" section
   - Click "Select Image File" and choose your ID card template
   - Supported formats: PNG, JPG, JPEG, GIF, BMP, WEBP, TIFF
   - Click "Upload Template"

3. **Activate Data**:
   - Select your uploaded dataset and click "Activate Selected"
   - Select your uploaded template and click "Activate"

### 2. Generate QR Codes

1. Go to "QR Codes" section
2. Click "Generate All QR Codes" to create QR codes for all employees
3. Or select specific employees and click "Generate Selected"
4. Monitor progress in the progress bar

### 3. Preview ID Cards

1. Go to "Preview" section
2. Select an employee from the dropdown
3. Adjust QR code position (X, Y) and size as needed
4. Click "Update Preview" to see the result
5. Use "Save Preview" to save individual cards
6. Use "Export All Cards" to save all ID cards

### 4. Email QR Codes (Optional)

1. Go to "Email" section
2. Configure email settings:
   - SMTP Server (e.g., smtp.gmail.com)
   - Port (usually 587 for TLS)
   - Username and password
   - Enable TLS if required
3. Click "Test Email Connection" to verify settings
4. Select employees and click "Send to Selected" or "Send to All"

### 5. Settings and Maintenance

1. Go to "Settings" section
2. Change appearance mode (Light/Dark/System)
3. Clean up old QR codes or all data if needed

## File Structure

```
your-folder/
├── desktop_app.py              # Main application
├── run_desktop_app.py          # Launcher script
├── requirements_desktop.txt    # Dependencies
├── qr_key.key                 # Encryption key (auto-generated)
├── data/                      # Application data
│   ├── qr_codes/             # Generated QR codes
│   ├── participant_list/     # Uploaded datasets
│   ├── id_templates/         # Uploaded templates
│   └── desktop_config.json   # Application settings
└── DESKTOP_README.md          # This file
```

## CSV Dataset Format

Your CSV file should have these columns:

| ID | Name | Position | Company | Email (optional) |
|----|------|----------|---------|------------------|
| 001 | John Doe | Manager | ABC Corp | <EMAIL> |
| 002 | Jane Smith | Developer | ABC Corp | <EMAIL> |

## Email Configuration

### Gmail Setup
1. Enable 2-factor authentication
2. Generate an "App Password" for this application
3. Use your Gmail address as username
4. Use the app password (not your regular password)
5. Server: smtp.gmail.com, Port: 587, TLS: Yes

### Other Email Providers
- **Outlook**: smtp-mail.outlook.com, Port: 587
- **Yahoo**: smtp.mail.yahoo.com, Port: 587
- **Custom**: Contact your email provider for SMTP settings

## Troubleshooting

### Common Issues

1. **"Module not found" error**:
   - Run `python run_desktop_app.py` to auto-install dependencies
   - Or manually install: `pip install -r requirements_desktop.txt`

2. **Email sending fails**:
   - Check internet connection
   - Verify email settings with "Test Email Connection"
   - For Gmail, use App Password instead of regular password

3. **Large datasets slow**:
   - The application is optimized for datasets up to 1000 employees
   - For larger datasets, consider splitting into smaller files

4. **QR codes not generating**:
   - Ensure dataset is activated
   - Check that CSV has required columns
   - Monitor memory usage in status bar

### Performance Tips

- Close other applications when processing large datasets
- Use PNG templates for best quality and compatibility
- Generate QR codes in batches rather than all at once for very large datasets

## Security Notes

- QR codes are encrypted using Fernet encryption
- The encryption key is stored locally in `qr_key.key`
- Keep this key file secure and backed up
- Email passwords are stored in local configuration (consider using app passwords)

## Support

For issues or questions:
1. Check this README file
2. Verify all requirements are met
3. Try the troubleshooting steps above
4. Check the console output for error messages

## Version History

- **v1.0**: Initial desktop application release
  - Local operation with modern UI
  - Dataset and template management
  - QR code generation and preview
  - Email integration
  - Memory optimization
