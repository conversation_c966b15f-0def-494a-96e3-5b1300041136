"""
Smart QR ID Scanner - Desktop Application
A local desktop application for QR code ID management and printing.
Works offline except for email functionality.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import sys
import threading
import json
import pandas as pd
import qrcode
from PIL import Image, ImageTk, ImageDraw, ImageFont
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from cryptography.fernet import Fernet
import time
from datetime import datetime
import shutil
import psutil
import gc
from pathlib import Path

# Set appearance mode and color theme
ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class QRIDDesktopApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Smart QR ID Scanner - Desktop")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # Initialize paths
        self.base_dir = Path(__file__).parent
        self.data_dir = self.base_dir / "data"
        self.qr_folder = self.data_dir / "qr_codes"
        self.dataset_dir = self.data_dir / "participant_list"
        self.template_dir = self.data_dir / "id_templates"
        self.config_file = self.data_dir / "desktop_config.json"
        
        # Create directories
        self.create_directories()
        
        # Initialize encryption
        self.init_encryption()
        
        # Load configuration
        self.config = self.load_config()
        
        # Initialize variables
        self.current_dataset = None
        self.current_template = None
        self.qr_generation_progress = {"active": False, "progress": 0, "total": 0}
        
        # Setup UI
        self.setup_ui()
        
        # Load initial data
        self.refresh_data()
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.data_dir,
            self.qr_folder,
            self.dataset_dir,
            self.template_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def init_encryption(self):
        """Initialize encryption for QR codes"""
        key_file = self.base_dir / "qr_key.key"
        
        if not key_file.exists():
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, "wb") as f:
                f.write(key)
        
        with open(key_file, "rb") as f:
            self.fernet = Fernet(f.read())
    
    def load_config(self):
        """Load application configuration"""
        if self.config_file.exists():
            try:
                with open(self.config_file, "r") as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "active_dataset": None,
            "active_template": None,
            "email_config": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "use_tls": True
            }
        }
    
    def save_config(self):
        """Save application configuration"""
        with open(self.config_file, "w") as f:
            json.dump(self.config, f, indent=2)
    
    def setup_ui(self):
        """Setup the main user interface"""
        # Configure grid weights
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create main content area
        self.create_main_content()
        
        # Create status bar
        self.create_status_bar()
    
    def create_sidebar(self):
        """Create the navigation sidebar"""
        self.sidebar_frame = ctk.CTkFrame(self.root, width=200, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=2, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(10, weight=1)
        
        # Logo/Title
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="QR ID Manager", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("Dashboard", "dashboard"),
            ("Dataset", "dataset"),
            ("Templates", "templates"),
            ("QR Codes", "qr_codes"),
            ("Preview", "preview"),
            ("Email", "email"),
            ("Settings", "settings")
        ]
        
        for i, (text, key) in enumerate(nav_items, 1):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=lambda k=key: self.show_section(k),
                height=40,
                font=ctk.CTkFont(size=14)
            )
            btn.grid(row=i, column=0, padx=20, pady=5, sticky="ew")
            self.nav_buttons[key] = btn
        
        # System info
        self.system_info_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="System Ready",
            font=ctk.CTkFont(size=12)
        )
        self.system_info_label.grid(row=11, column=0, padx=20, pady=10)
    
    def create_main_content(self):
        """Create the main content area"""
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=10, pady=10)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # Create content frames for each section
        self.content_frames = {}
        self.create_dashboard_frame()
        self.create_dataset_frame()
        self.create_templates_frame()
        self.create_qr_codes_frame()
        self.create_preview_frame()
        self.create_email_frame()
        self.create_settings_frame()
        
        # Show dashboard by default
        self.show_section("dashboard")
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.grid(row=1, column=1, sticky="ew", padx=10, pady=(0, 10))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Memory usage indicator
        self.memory_label = ctk.CTkLabel(
            self.status_frame,
            text="Memory: 0%",
            font=ctk.CTkFont(size=12)
        )
        self.memory_label.pack(side="right", padx=10, pady=5)
        
        # Start memory monitoring
        self.update_memory_usage()
    
    def update_memory_usage(self):
        """Update memory usage display"""
        try:
            memory = psutil.virtual_memory()
            self.memory_label.configure(text=f"Memory: {memory.percent:.1f}%")
        except:
            pass
        
        # Schedule next update
        self.root.after(5000, self.update_memory_usage)
    
    def show_section(self, section_key):
        """Show the specified section"""
        # Hide all frames
        for frame in self.content_frames.values():
            frame.grid_remove()
        
        # Show selected frame
        if section_key in self.content_frames:
            self.content_frames[section_key].grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        
        # Update button states
        for key, btn in self.nav_buttons.items():
            if key == section_key:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))
    
    def create_dashboard_frame(self):
        """Create the dashboard frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["dashboard"] = frame
        
        # Title
        title = ctk.CTkLabel(
            frame,
            text="Dashboard",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # Stats frame
        stats_frame = ctk.CTkFrame(frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        # Create stats cards
        self.create_stats_cards(stats_frame)
        
        # Quick actions
        actions_frame = ctk.CTkFrame(frame)
        actions_frame.pack(fill="x", padx=20, pady=10)
        
        actions_title = ctk.CTkLabel(
            actions_frame,
            text="Quick Actions",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        actions_title.pack(pady=10)
        
        # Action buttons
        btn_frame = ctk.CTkFrame(actions_frame)
        btn_frame.pack(pady=10)
        
        quick_actions = [
            ("Upload Dataset", lambda: self.show_section("dataset")),
            ("Upload Template", lambda: self.show_section("templates")),
            ("Generate QR Codes", lambda: self.show_section("qr_codes")),
            ("Preview Cards", lambda: self.show_section("preview"))
        ]
        
        for i, (text, command) in enumerate(quick_actions):
            btn = ctk.CTkButton(
                btn_frame,
                text=text,
                command=command,
                width=150,
                height=40
            )
            btn.grid(row=i//2, column=i%2, padx=10, pady=5)
    
    def create_stats_cards(self, parent):
        """Create statistics cards"""
        # Configure grid
        parent.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # Stats data
        stats = [
            ("Datasets", self.get_dataset_count()),
            ("Templates", self.get_template_count()),
            ("QR Codes", self.get_qr_count()),
            ("Employees", self.get_employee_count())
        ]
        
        for i, (label, value) in enumerate(stats):
            card = ctk.CTkFrame(parent)
            card.grid(row=0, column=i, padx=10, pady=10, sticky="ew")
            
            value_label = ctk.CTkLabel(
                card,
                text=str(value),
                font=ctk.CTkFont(size=24, weight="bold")
            )
            value_label.pack(pady=(10, 5))
            
            label_label = ctk.CTkLabel(
                card,
                text=label,
                font=ctk.CTkFont(size=14)
            )
            label_label.pack(pady=(0, 10))
    
    def get_dataset_count(self):
        """Get number of datasets"""
        try:
            return len(list(self.dataset_dir.glob("*.csv")))
        except:
            return 0
    
    def get_template_count(self):
        """Get number of templates"""
        try:
            return len(list(self.template_dir.glob("*.png"))) + len(list(self.template_dir.glob("*.jpg")))
        except:
            return 0
    
    def get_qr_count(self):
        """Get number of QR codes"""
        try:
            return len(list(self.qr_folder.glob("*.png")))
        except:
            return 0
    
    def get_employee_count(self):
        """Get number of employees in active dataset"""
        if self.current_dataset is not None:
            return len(self.current_dataset)
        return 0
    
    def refresh_data(self):
        """Refresh all data"""
        # Load active dataset
        if self.config.get("active_dataset"):
            dataset_path = self.dataset_dir / self.config["active_dataset"]
            if dataset_path.exists():
                try:
                    self.current_dataset = pd.read_csv(dataset_path)
                except:
                    self.current_dataset = None
        
        # Load active template
        if self.config.get("active_template"):
            template_path = self.template_dir / self.config["active_template"]
            if template_path.exists():
                try:
                    self.current_template = Image.open(template_path)
                except:
                    self.current_template = None
        
        # Update UI
        self.update_status("Data refreshed")
    
    def update_status(self, message):
        """Update status bar message"""
        self.status_label.configure(text=message)
        # Auto-clear after 3 seconds
        self.root.after(3000, lambda: self.status_label.configure(text="Ready"))
    
    def create_dataset_frame(self):
        """Create the dataset management frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["dataset"] = frame

        # Title
        title = ctk.CTkLabel(
            frame,
            text="Dataset Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # Upload section
        upload_frame = ctk.CTkFrame(frame)
        upload_frame.pack(fill="x", padx=20, pady=10)

        upload_title = ctk.CTkLabel(
            upload_frame,
            text="Upload New Dataset",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        upload_title.pack(pady=10)

        # File selection
        file_frame = ctk.CTkFrame(upload_frame)
        file_frame.pack(fill="x", padx=20, pady=10)

        self.dataset_file_label = ctk.CTkLabel(
            file_frame,
            text="No file selected",
            font=ctk.CTkFont(size=12)
        )
        self.dataset_file_label.pack(side="left", padx=10, pady=10)

        select_btn = ctk.CTkButton(
            file_frame,
            text="Select CSV File",
            command=self.select_dataset_file,
            width=120
        )
        select_btn.pack(side="right", padx=10, pady=10)

        # Upload button
        upload_btn = ctk.CTkButton(
            upload_frame,
            text="Upload Dataset",
            command=self.upload_dataset,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        upload_btn.pack(pady=10)

        # Existing datasets
        existing_frame = ctk.CTkFrame(frame)
        existing_frame.pack(fill="both", expand=True, padx=20, pady=10)

        existing_title = ctk.CTkLabel(
            existing_frame,
            text="Existing Datasets",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        existing_title.pack(pady=10)

        # Dataset list
        self.dataset_listbox = tk.Listbox(existing_frame, height=8)
        self.dataset_listbox.pack(fill="both", expand=True, padx=20, pady=10)

        # Dataset actions
        dataset_actions_frame = ctk.CTkFrame(existing_frame)
        dataset_actions_frame.pack(fill="x", padx=20, pady=10)

        activate_btn = ctk.CTkButton(
            dataset_actions_frame,
            text="Activate Selected",
            command=self.activate_dataset,
            width=120
        )
        activate_btn.pack(side="left", padx=5)

        preview_btn = ctk.CTkButton(
            dataset_actions_frame,
            text="Preview",
            command=self.preview_dataset,
            width=120
        )
        preview_btn.pack(side="left", padx=5)

        delete_btn = ctk.CTkButton(
            dataset_actions_frame,
            text="Delete",
            command=self.delete_dataset,
            width=120,
            fg_color="red"
        )
        delete_btn.pack(side="right", padx=5)

        # Refresh dataset list
        self.refresh_dataset_list()

    def select_dataset_file(self):
        """Select a dataset file"""
        file_path = filedialog.askopenfilename(
            title="Select Dataset CSV File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            self.selected_dataset_file = file_path
            filename = os.path.basename(file_path)
            self.dataset_file_label.configure(text=f"Selected: {filename}")
        else:
            self.selected_dataset_file = None
            self.dataset_file_label.configure(text="No file selected")

    def upload_dataset(self):
        """Upload and process the selected dataset"""
        if not hasattr(self, 'selected_dataset_file') or not self.selected_dataset_file:
            messagebox.showerror("Error", "Please select a CSV file first")
            return

        try:
            # Read and validate the dataset
            df = pd.read_csv(self.selected_dataset_file)

            # Validate required columns
            required_columns = ['ID', 'Name', 'Position', 'Company']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror(
                    "Error",
                    f"Missing required columns: {', '.join(missing_columns)}"
                )
                return

            # Generate encrypted QR data
            for index, row in df.iterrows():
                qr_data = f"id={str(row['ID']).zfill(3)};name={row['Name']};position={row['Position']};company={row['Company']}"
                encrypted_qr = self.fernet.encrypt(qr_data.encode()).decode()
                df.at[index, 'EncryptedQR'] = encrypted_qr

            # Save the dataset
            timestamp = int(time.time())
            filename = f"{timestamp}_{os.path.basename(self.selected_dataset_file)}"
            dataset_path = self.dataset_dir / filename

            df.to_csv(dataset_path, index=False)

            # Update UI
            self.refresh_dataset_list()
            self.update_status(f"Dataset uploaded: {filename}")

            # Clear selection
            self.selected_dataset_file = None
            self.dataset_file_label.configure(text="No file selected")

            messagebox.showinfo("Success", f"Dataset uploaded successfully!\n{len(df)} employees processed.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to upload dataset: {str(e)}")

    def refresh_dataset_list(self):
        """Refresh the dataset list"""
        self.dataset_listbox.delete(0, tk.END)

        try:
            csv_files = list(self.dataset_dir.glob("*.csv"))
            csv_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            for file_path in csv_files:
                display_name = file_path.name
                if file_path.name == self.config.get("active_dataset"):
                    display_name += " (Active)"
                self.dataset_listbox.insert(tk.END, display_name)
        except Exception as e:
            print(f"Error refreshing dataset list: {e}")

    def activate_dataset(self):
        """Activate the selected dataset"""
        selection = self.dataset_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a dataset to activate")
            return

        selected_item = self.dataset_listbox.get(selection[0])
        filename = selected_item.replace(" (Active)", "")

        try:
            # Load and validate the dataset
            dataset_path = self.dataset_dir / filename
            df = pd.read_csv(dataset_path)

            # Update configuration
            self.config["active_dataset"] = filename
            self.save_config()

            # Update current dataset
            self.current_dataset = df

            # Refresh UI
            self.refresh_dataset_list()
            self.update_status(f"Activated dataset: {filename}")

            messagebox.showinfo("Success", f"Dataset activated: {filename}\n{len(df)} employees loaded.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to activate dataset: {str(e)}")

    def preview_dataset(self):
        """Preview the selected dataset"""
        selection = self.dataset_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a dataset to preview")
            return

        selected_item = self.dataset_listbox.get(selection[0])
        filename = selected_item.replace(" (Active)", "")

        try:
            dataset_path = self.dataset_dir / filename
            df = pd.read_csv(dataset_path)

            # Create preview window
            preview_window = ctk.CTkToplevel(self.root)
            preview_window.title(f"Dataset Preview: {filename}")
            preview_window.geometry("800x600")

            # Create treeview for data display
            tree_frame = ctk.CTkFrame(preview_window)
            tree_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # Create treeview
            columns = list(df.columns)
            tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

            # Configure columns
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=100)

            # Add data
            for _, row in df.head(100).iterrows():  # Show first 100 rows
                tree.insert("", "end", values=list(row))

            # Add scrollbars
            v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
            h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
            tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

            # Pack everything
            tree.pack(side="left", fill="both", expand=True)
            v_scrollbar.pack(side="right", fill="y")
            h_scrollbar.pack(side="bottom", fill="x")

            # Info label
            info_label = ctk.CTkLabel(
                preview_window,
                text=f"Showing first 100 rows of {len(df)} total employees",
                font=ctk.CTkFont(size=12)
            )
            info_label.pack(pady=10)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview dataset: {str(e)}")

    def delete_dataset(self):
        """Delete the selected dataset"""
        selection = self.dataset_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a dataset to delete")
            return

        selected_item = self.dataset_listbox.get(selection[0])
        filename = selected_item.replace(" (Active)", "")

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{filename}'?"):
            return

        try:
            dataset_path = self.dataset_dir / filename
            dataset_path.unlink()

            # If this was the active dataset, clear it
            if self.config.get("active_dataset") == filename:
                self.config["active_dataset"] = None
                self.current_dataset = None
                self.save_config()

            # Refresh UI
            self.refresh_dataset_list()
            self.update_status(f"Deleted dataset: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete dataset: {str(e)}")

    def create_templates_frame(self):
        """Create the template management frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["templates"] = frame

        # Title
        title = ctk.CTkLabel(
            frame,
            text="Template Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # Upload section
        upload_frame = ctk.CTkFrame(frame)
        upload_frame.pack(fill="x", padx=20, pady=10)

        upload_title = ctk.CTkLabel(
            upload_frame,
            text="Upload New Template",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        upload_title.pack(pady=10)

        # File selection
        file_frame = ctk.CTkFrame(upload_frame)
        file_frame.pack(fill="x", padx=20, pady=10)

        self.template_file_label = ctk.CTkLabel(
            file_frame,
            text="No file selected",
            font=ctk.CTkFont(size=12)
        )
        self.template_file_label.pack(side="left", padx=10, pady=10)

        select_btn = ctk.CTkButton(
            file_frame,
            text="Select Image File",
            command=self.select_template_file,
            width=120
        )
        select_btn.pack(side="right", padx=10, pady=10)

        # Upload button
        upload_btn = ctk.CTkButton(
            upload_frame,
            text="Upload Template",
            command=self.upload_template,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        upload_btn.pack(pady=10)

        # Template list and preview
        content_frame = ctk.CTkFrame(frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        content_frame.grid_columnconfigure(1, weight=1)

        # Template list
        list_frame = ctk.CTkFrame(content_frame)
        list_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        list_title = ctk.CTkLabel(
            list_frame,
            text="Templates",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        list_title.pack(pady=10)

        self.template_listbox = tk.Listbox(list_frame, height=12, width=30)
        self.template_listbox.pack(fill="both", expand=True, padx=10, pady=10)
        self.template_listbox.bind("<<ListboxSelect>>", self.on_template_select)

        # Template actions
        template_actions_frame = ctk.CTkFrame(list_frame)
        template_actions_frame.pack(fill="x", padx=10, pady=10)

        activate_btn = ctk.CTkButton(
            template_actions_frame,
            text="Activate",
            command=self.activate_template,
            width=80
        )
        activate_btn.pack(side="left", padx=2)

        delete_btn = ctk.CTkButton(
            template_actions_frame,
            text="Delete",
            command=self.delete_template,
            width=80,
            fg_color="red"
        )
        delete_btn.pack(side="right", padx=2)

        # Preview area
        preview_frame = ctk.CTkFrame(content_frame)
        preview_frame.grid(row=0, column=1, sticky="nsew")

        preview_title = ctk.CTkLabel(
            preview_frame,
            text="Preview",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        preview_title.pack(pady=10)

        # Preview canvas
        self.template_preview_label = ctk.CTkLabel(
            preview_frame,
            text="Select a template to preview",
            width=400,
            height=300
        )
        self.template_preview_label.pack(pady=20, padx=20)

        # Refresh template list
        self.refresh_template_list()

    def select_template_file(self):
        """Select a template file"""
        file_path = filedialog.askopenfilename(
            title="Select Template Image File",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.webp *.tiff *.tif"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.selected_template_file = file_path
            filename = os.path.basename(file_path)
            self.template_file_label.configure(text=f"Selected: {filename}")
        else:
            self.selected_template_file = None
            self.template_file_label.configure(text="No file selected")

    def upload_template(self):
        """Upload and process the selected template"""
        if not hasattr(self, 'selected_template_file') or not self.selected_template_file:
            messagebox.showerror("Error", "Please select an image file first")
            return

        try:
            # Validate and optimize the image
            with Image.open(self.selected_template_file) as img:
                # Convert to RGBA for consistency
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')

                # Resize if too large (max 2048px)
                max_size = 2048
                if img.width > max_size or img.height > max_size:
                    img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

                # Save the template
                timestamp = int(time.time())
                original_name = os.path.splitext(os.path.basename(self.selected_template_file))[0]
                filename = f"{timestamp}_{original_name}.png"
                template_path = self.template_dir / filename

                img.save(template_path, 'PNG', optimize=True)

            # Update UI
            self.refresh_template_list()
            self.update_status(f"Template uploaded: {filename}")

            # Clear selection
            self.selected_template_file = None
            self.template_file_label.configure(text="No file selected")

            messagebox.showinfo("Success", f"Template uploaded successfully!\nSaved as: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to upload template: {str(e)}")

    def refresh_template_list(self):
        """Refresh the template list"""
        self.template_listbox.delete(0, tk.END)

        try:
            image_files = []
            for ext in ['*.png', '*.jpg', '*.jpeg', '*.gif', '*.bmp', '*.webp', '*.tiff', '*.tif']:
                image_files.extend(self.template_dir.glob(ext))

            image_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            for file_path in image_files:
                display_name = file_path.name
                if file_path.name == self.config.get("active_template"):
                    display_name += " (Active)"
                self.template_listbox.insert(tk.END, display_name)
        except Exception as e:
            print(f"Error refreshing template list: {e}")

    def on_template_select(self, event):
        """Handle template selection"""
        selection = self.template_listbox.curselection()
        if not selection:
            return

        selected_item = self.template_listbox.get(selection[0])
        filename = selected_item.replace(" (Active)", "")

        try:
            template_path = self.template_dir / filename

            # Load and resize image for preview
            with Image.open(template_path) as img:
                # Calculate preview size (max 400x300)
                preview_width, preview_height = 400, 300
                img_ratio = img.width / img.height
                preview_ratio = preview_width / preview_height

                if img_ratio > preview_ratio:
                    # Image is wider
                    new_width = preview_width
                    new_height = int(preview_width / img_ratio)
                else:
                    # Image is taller
                    new_height = preview_height
                    new_width = int(preview_height * img_ratio)

                preview_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(preview_img)

                # Update preview
                self.template_preview_label.configure(image=photo, text="")
                self.template_preview_label.image = photo  # Keep a reference

        except Exception as e:
            self.template_preview_label.configure(image="", text=f"Error loading preview: {str(e)}")
            print(f"Error loading template preview: {e}")

    def activate_template(self):
        """Activate the selected template"""
        selection = self.template_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to activate")
            return

        selected_item = self.template_listbox.get(selection[0])
        filename = selected_item.replace(" (Active)", "")

        try:
            # Validate the template
            template_path = self.template_dir / filename
            with Image.open(template_path) as img:
                # Update configuration
                self.config["active_template"] = filename
                self.save_config()

                # Update current template
                self.current_template = img.copy()

                # Refresh UI
                self.refresh_template_list()
                self.update_status(f"Activated template: {filename}")

                messagebox.showinfo("Success", f"Template activated: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to activate template: {str(e)}")

    def delete_template(self):
        """Delete the selected template"""
        selection = self.template_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to delete")
            return

        selected_item = self.template_listbox.get(selection[0])
        filename = selected_item.replace(" (Active)", "")

        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{filename}'?"):
            return

        try:
            template_path = self.template_dir / filename
            template_path.unlink()

            # If this was the active template, clear it
            if self.config.get("active_template") == filename:
                self.config["active_template"] = None
                self.current_template = None
                self.save_config()

            # Refresh UI
            self.refresh_template_list()
            self.template_preview_label.configure(image="", text="Select a template to preview")
            self.update_status(f"Deleted template: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete template: {str(e)}")

    def create_qr_codes_frame(self):
        """Create the QR codes management frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["qr_codes"] = frame

        # Title
        title = ctk.CTkLabel(
            frame,
            text="QR Code Generation",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # Status section
        status_frame = ctk.CTkFrame(frame)
        status_frame.pack(fill="x", padx=20, pady=10)

        # Current status
        self.qr_status_label = ctk.CTkLabel(
            status_frame,
            text="Ready to generate QR codes",
            font=ctk.CTkFont(size=14)
        )
        self.qr_status_label.pack(pady=10)

        # Progress bar
        self.qr_progress = ctk.CTkProgressBar(status_frame)
        self.qr_progress.pack(fill="x", padx=20, pady=5)
        self.qr_progress.set(0)

        # Generation controls
        controls_frame = ctk.CTkFrame(frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        controls_title = ctk.CTkLabel(
            controls_frame,
            text="Generation Controls",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        controls_title.pack(pady=10)

        # Buttons frame
        buttons_frame = ctk.CTkFrame(controls_frame)
        buttons_frame.pack(pady=10)

        # Generate all button
        self.generate_all_btn = ctk.CTkButton(
            buttons_frame,
            text="Generate All QR Codes",
            command=self.generate_all_qr_codes,
            height=40,
            width=200,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.generate_all_btn.pack(side="left", padx=10)

        # Generate selected button
        self.generate_selected_btn = ctk.CTkButton(
            buttons_frame,
            text="Generate Selected",
            command=self.generate_selected_qr_codes,
            height=40,
            width=200
        )
        self.generate_selected_btn.pack(side="left", padx=10)

        # Stop button
        self.stop_generation_btn = ctk.CTkButton(
            buttons_frame,
            text="Stop Generation",
            command=self.stop_qr_generation,
            height=40,
            width=150,
            fg_color="red",
            state="disabled"
        )
        self.stop_generation_btn.pack(side="left", padx=10)

        # Employee list and QR status
        content_frame = ctk.CTkFrame(frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        content_frame.grid_columnconfigure(1, weight=1)

        # Employee list
        list_frame = ctk.CTkFrame(content_frame)
        list_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        list_title = ctk.CTkLabel(
            list_frame,
            text="Employees",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        list_title.pack(pady=10)

        # Create treeview for employee list
        self.employee_tree = ttk.Treeview(
            list_frame,
            columns=("ID", "Name", "QR Status"),
            show="headings",
            height=15
        )

        # Configure columns
        self.employee_tree.heading("ID", text="ID")
        self.employee_tree.heading("Name", text="Name")
        self.employee_tree.heading("QR Status", text="QR Status")

        self.employee_tree.column("ID", width=80)
        self.employee_tree.column("Name", width=150)
        self.employee_tree.column("QR Status", width=100)

        self.employee_tree.pack(fill="both", expand=True, padx=10, pady=10)

        # QR preview area
        preview_frame = ctk.CTkFrame(content_frame)
        preview_frame.grid(row=0, column=1, sticky="nsew")

        preview_title = ctk.CTkLabel(
            preview_frame,
            text="QR Code Preview",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        preview_title.pack(pady=10)

        # QR preview
        self.qr_preview_label = ctk.CTkLabel(
            preview_frame,
            text="Select an employee to preview QR code",
            width=300,
            height=300
        )
        self.qr_preview_label.pack(pady=20, padx=20)

        # QR actions
        qr_actions_frame = ctk.CTkFrame(preview_frame)
        qr_actions_frame.pack(fill="x", padx=20, pady=10)

        save_qr_btn = ctk.CTkButton(
            qr_actions_frame,
            text="Save QR Code",
            command=self.save_qr_code,
            width=120
        )
        save_qr_btn.pack(side="left", padx=5)

        regenerate_qr_btn = ctk.CTkButton(
            qr_actions_frame,
            text="Regenerate",
            command=self.regenerate_qr_code,
            width=120
        )
        regenerate_qr_btn.pack(side="right", padx=5)

        # Bind selection event
        self.employee_tree.bind("<<TreeviewSelect>>", self.on_employee_select)

        # Refresh employee list
        self.refresh_employee_list()

    def refresh_employee_list(self):
        """Refresh the employee list with QR status"""
        # Clear existing items
        for item in self.employee_tree.get_children():
            self.employee_tree.delete(item)

        if self.current_dataset is None:
            self.qr_status_label.configure(text="No active dataset. Please activate a dataset first.")
            return

        # Add employees with QR status
        for _, row in self.current_dataset.iterrows():
            employee_id = str(row['ID'])
            name = str(row['Name'])

            # Check if QR code exists
            qr_path = self.qr_folder / f"{employee_id}.png"
            qr_status = "Generated" if qr_path.exists() else "Pending"

            self.employee_tree.insert("", "end", values=(employee_id, name, qr_status))

        # Update status
        total_employees = len(self.current_dataset)
        generated_count = len(list(self.qr_folder.glob("*.png")))
        self.qr_status_label.configure(
            text=f"QR Codes: {generated_count}/{total_employees} generated"
        )

    def generate_all_qr_codes(self):
        """Generate QR codes for all employees"""
        if self.current_dataset is None:
            messagebox.showwarning("Warning", "No active dataset. Please activate a dataset first.")
            return

        if self.qr_generation_progress["active"]:
            messagebox.showwarning("Warning", "QR generation is already in progress.")
            return

        # Start generation in background thread
        self.start_qr_generation(list(self.current_dataset['ID'].astype(str)))

    def generate_selected_qr_codes(self):
        """Generate QR codes for selected employees"""
        if self.current_dataset is None:
            messagebox.showwarning("Warning", "No active dataset. Please activate a dataset first.")
            return

        selected_items = self.employee_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "Please select employees to generate QR codes for.")
            return

        # Get selected employee IDs
        employee_ids = []
        for item in selected_items:
            values = self.employee_tree.item(item, "values")
            employee_ids.append(values[0])  # ID is the first column

        # Start generation
        self.start_qr_generation(employee_ids)

    def start_qr_generation(self, employee_ids):
        """Start QR code generation in background thread"""
        self.qr_generation_progress = {
            "active": True,
            "progress": 0,
            "total": len(employee_ids),
            "current_id": None
        }

        # Update UI
        self.generate_all_btn.configure(state="disabled")
        self.generate_selected_btn.configure(state="disabled")
        self.stop_generation_btn.configure(state="normal")

        # Start background thread
        thread = threading.Thread(
            target=self.qr_generation_worker,
            args=(employee_ids,),
            daemon=True
        )
        thread.start()

        # Start progress monitoring
        self.monitor_qr_progress()

    def qr_generation_worker(self, employee_ids):
        """Background worker for QR generation"""
        try:
            for i, employee_id in enumerate(employee_ids):
                if not self.qr_generation_progress["active"]:
                    break

                self.qr_generation_progress["current_id"] = employee_id

                # Find employee data
                employee_row = self.current_dataset[self.current_dataset['ID'].astype(str) == employee_id]
                if employee_row.empty:
                    continue

                employee_data = employee_row.iloc[0]

                # Generate QR code
                qr_path = self.qr_folder / f"{employee_id}.png"

                try:
                    qr_img = qrcode.make(employee_data['EncryptedQR'])
                    qr_img.save(qr_path)
                except Exception as e:
                    print(f"Failed to generate QR for {employee_id}: {e}")

                self.qr_generation_progress["progress"] = i + 1

                # Brief pause to prevent overwhelming the system
                time.sleep(0.1)

        except Exception as e:
            print(f"QR generation worker error: {e}")
        finally:
            self.qr_generation_progress["active"] = False

    def monitor_qr_progress(self):
        """Monitor QR generation progress"""
        if self.qr_generation_progress["active"]:
            progress = self.qr_generation_progress["progress"]
            total = self.qr_generation_progress["total"]
            current_id = self.qr_generation_progress["current_id"]

            # Update progress bar
            if total > 0:
                self.qr_progress.set(progress / total)

            # Update status
            if current_id:
                self.qr_status_label.configure(
                    text=f"Generating QR codes... {progress}/{total} (Current: {current_id})"
                )

            # Schedule next update
            self.root.after(500, self.monitor_qr_progress)
        else:
            # Generation complete
            self.qr_progress.set(1.0)
            self.qr_status_label.configure(text="QR generation completed!")

            # Reset UI
            self.generate_all_btn.configure(state="normal")
            self.generate_selected_btn.configure(state="normal")
            self.stop_generation_btn.configure(state="disabled")

            # Refresh employee list
            self.refresh_employee_list()

            # Reset progress after a delay
            self.root.after(2000, lambda: self.qr_progress.set(0))

    def stop_qr_generation(self):
        """Stop QR code generation"""
        self.qr_generation_progress["active"] = False
        self.qr_status_label.configure(text="QR generation stopped by user")

    def on_employee_select(self, event):
        """Handle employee selection for QR preview"""
        selected_items = self.employee_tree.selection()
        if not selected_items:
            return

        # Get selected employee
        item = selected_items[0]
        values = self.employee_tree.item(item, "values")
        employee_id = values[0]

        # Load QR code if it exists
        qr_path = self.qr_folder / f"{employee_id}.png"

        if qr_path.exists():
            try:
                # Load and resize QR code for preview
                with Image.open(qr_path) as qr_img:
                    # Resize to fit preview area
                    preview_size = 250
                    qr_img = qr_img.resize((preview_size, preview_size), Image.Resampling.NEAREST)

                    # Convert to PhotoImage
                    photo = ImageTk.PhotoImage(qr_img)

                    # Update preview
                    self.qr_preview_label.configure(image=photo, text="")
                    self.qr_preview_label.image = photo  # Keep a reference

            except Exception as e:
                self.qr_preview_label.configure(image="", text=f"Error loading QR code: {str(e)}")
        else:
            self.qr_preview_label.configure(image="", text="QR code not generated yet")

    def save_qr_code(self):
        """Save the currently previewed QR code"""
        selected_items = self.employee_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "Please select an employee first")
            return

        item = selected_items[0]
        values = self.employee_tree.item(item, "values")
        employee_id = values[0]
        employee_name = values[1]

        qr_path = self.qr_folder / f"{employee_id}.png"

        if not qr_path.exists():
            messagebox.showwarning("Warning", "QR code not generated yet")
            return

        # Ask user where to save
        save_path = filedialog.asksaveasfilename(
            title="Save QR Code",
            defaultextension=".png",
            initialname=f"QR_{employee_id}_{employee_name.replace(' ', '_')}.png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )

        if save_path:
            try:
                shutil.copy2(qr_path, save_path)
                messagebox.showinfo("Success", f"QR code saved to: {save_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save QR code: {str(e)}")

    def regenerate_qr_code(self):
        """Regenerate QR code for selected employee"""
        selected_items = self.employee_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "Please select an employee first")
            return

        item = selected_items[0]
        values = self.employee_tree.item(item, "values")
        employee_id = values[0]

        # Start generation for this employee
        self.start_qr_generation([employee_id])

    def create_preview_frame(self):
        """Create the preview frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["preview"] = frame

        # Title
        title = ctk.CTkLabel(
            frame,
            text="ID Card Preview",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # Controls
        controls_frame = ctk.CTkFrame(frame)
        controls_frame.pack(fill="x", padx=20, pady=10)

        # Employee selection
        selection_frame = ctk.CTkFrame(controls_frame)
        selection_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            selection_frame,
            text="Select Employee:",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(side="left", padx=10)

        self.preview_employee_var = ctk.StringVar()
        self.preview_employee_combo = ctk.CTkComboBox(
            selection_frame,
            variable=self.preview_employee_var,
            command=self.update_preview,
            width=200
        )
        self.preview_employee_combo.pack(side="left", padx=10)

        # QR position controls
        position_frame = ctk.CTkFrame(controls_frame)
        position_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            position_frame,
            text="QR Position:",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(side="left", padx=10)

        # X position
        ctk.CTkLabel(position_frame, text="X:").pack(side="left", padx=5)
        self.qr_x_var = ctk.StringVar(value="50")
        self.qr_x_entry = ctk.CTkEntry(position_frame, textvariable=self.qr_x_var, width=60)
        self.qr_x_entry.pack(side="left", padx=5)

        # Y position
        ctk.CTkLabel(position_frame, text="Y:").pack(side="left", padx=5)
        self.qr_y_var = ctk.StringVar(value="50")
        self.qr_y_entry = ctk.CTkEntry(position_frame, textvariable=self.qr_y_var, width=60)
        self.qr_y_entry.pack(side="left", padx=5)

        # QR size
        ctk.CTkLabel(position_frame, text="Size:").pack(side="left", padx=5)
        self.qr_size_var = ctk.StringVar(value="100")
        self.qr_size_entry = ctk.CTkEntry(position_frame, textvariable=self.qr_size_var, width=60)
        self.qr_size_entry.pack(side="left", padx=5)

        # Update button
        update_btn = ctk.CTkButton(
            position_frame,
            text="Update Preview",
            command=self.update_preview,
            width=120
        )
        update_btn.pack(side="left", padx=10)

        # Preview area
        preview_frame = ctk.CTkFrame(frame)
        preview_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # Preview canvas
        self.preview_canvas_label = ctk.CTkLabel(
            preview_frame,
            text="Select an employee and ensure both dataset and template are active",
            width=600,
            height=400
        )
        self.preview_canvas_label.pack(pady=20, padx=20)

        # Action buttons
        actions_frame = ctk.CTkFrame(preview_frame)
        actions_frame.pack(fill="x", padx=20, pady=10)

        save_preview_btn = ctk.CTkButton(
            actions_frame,
            text="Save Preview",
            command=self.save_preview,
            width=120
        )
        save_preview_btn.pack(side="left", padx=5)

        print_btn = ctk.CTkButton(
            actions_frame,
            text="Print ID Card",
            command=self.print_id_card,
            width=120
        )
        print_btn.pack(side="left", padx=5)

        export_all_btn = ctk.CTkButton(
            actions_frame,
            text="Export All Cards",
            command=self.export_all_cards,
            width=120
        )
        export_all_btn.pack(side="right", padx=5)

        # Refresh employee list for preview
        self.refresh_preview_employees()

    def refresh_preview_employees(self):
        """Refresh employee list for preview"""
        if self.current_dataset is None:
            self.preview_employee_combo.configure(values=["No dataset active"])
            return

        # Create employee list with ID and Name
        employees = []
        for _, row in self.current_dataset.iterrows():
            employees.append(f"{row['ID']} - {row['Name']}")

        self.preview_employee_combo.configure(values=employees)
        if employees:
            self.preview_employee_combo.set(employees[0])

    def update_preview(self, *args):
        """Update the ID card preview"""
        if self.current_dataset is None or self.current_template is None:
            self.preview_canvas_label.configure(
                image="",
                text="Please activate both a dataset and template"
            )
            return

        selected = self.preview_employee_var.get()
        if not selected or selected == "No dataset active":
            return

        try:
            # Extract employee ID
            employee_id = selected.split(" - ")[0]

            # Find employee data
            employee_row = self.current_dataset[self.current_dataset['ID'].astype(str) == employee_id]
            if employee_row.empty:
                return

            employee_data = employee_row.iloc[0]

            # Get QR position and size
            qr_x = int(self.qr_x_var.get() or 50)
            qr_y = int(self.qr_y_var.get() or 50)
            qr_size = int(self.qr_size_var.get() or 100)

            # Create preview image
            preview_img = self.create_id_card_preview(employee_data, qr_x, qr_y, qr_size)

            if preview_img:
                # Resize for display
                display_width, display_height = 500, 350
                img_ratio = preview_img.width / preview_img.height
                display_ratio = display_width / display_height

                if img_ratio > display_ratio:
                    new_width = display_width
                    new_height = int(display_width / img_ratio)
                else:
                    new_height = display_height
                    new_width = int(display_height * img_ratio)

                display_img = preview_img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(display_img)

                # Update preview
                self.preview_canvas_label.configure(image=photo, text="")
                self.preview_canvas_label.image = photo  # Keep a reference
                self.current_preview_image = preview_img  # Store full-size image

        except Exception as e:
            self.preview_canvas_label.configure(
                image="",
                text=f"Error creating preview: {str(e)}"
            )
            print(f"Preview error: {e}")

    def create_id_card_preview(self, employee_data, qr_x, qr_y, qr_size):
        """Create an ID card preview with QR code overlay"""
        try:
            # Start with template
            card_img = self.current_template.copy()

            # Check if QR code exists
            qr_path = self.qr_folder / f"{employee_data['ID']}.png"

            if qr_path.exists():
                # Load QR code
                with Image.open(qr_path) as qr_img:
                    # Resize QR code
                    qr_img = qr_img.resize((qr_size, qr_size), Image.Resampling.NEAREST)

                    # Paste QR code onto template
                    if qr_img.mode == 'RGBA':
                        card_img.paste(qr_img, (qr_x, qr_y), qr_img)
                    else:
                        card_img.paste(qr_img, (qr_x, qr_y))
            else:
                # Draw placeholder for missing QR code
                draw = ImageDraw.Draw(card_img)
                draw.rectangle(
                    [qr_x, qr_y, qr_x + qr_size, qr_y + qr_size],
                    outline="red",
                    width=2
                )

                # Add text
                try:
                    font = ImageFont.load_default()
                    draw.text(
                        (qr_x + 10, qr_y + qr_size//2),
                        "QR Not\nGenerated",
                        fill="red",
                        font=font
                    )
                except:
                    pass

            return card_img

        except Exception as e:
            print(f"Error creating ID card preview: {e}")
            return None

    def save_preview(self):
        """Save the current preview"""
        if not hasattr(self, 'current_preview_image') or self.current_preview_image is None:
            messagebox.showwarning("Warning", "No preview to save")
            return

        selected = self.preview_employee_var.get()
        if not selected:
            return

        employee_id = selected.split(" - ")[0]
        employee_name = selected.split(" - ")[1] if " - " in selected else "Unknown"

        # Ask user where to save
        save_path = filedialog.asksaveasfilename(
            title="Save ID Card Preview",
            defaultextension=".png",
            initialname=f"ID_Card_{employee_id}_{employee_name.replace(' ', '_')}.png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )

        if save_path:
            try:
                self.current_preview_image.save(save_path)
                messagebox.showinfo("Success", f"ID card saved to: {save_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save ID card: {str(e)}")

    def print_id_card(self):
        """Print the current ID card"""
        if not hasattr(self, 'current_preview_image') or self.current_preview_image is None:
            messagebox.showwarning("Warning", "No preview to print")
            return

        try:
            # Save to temporary file and open with default application
            import tempfile
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
                self.current_preview_image.save(tmp_file.name)

                # Open with default application (which should allow printing)
                if sys.platform.startswith('win'):
                    os.startfile(tmp_file.name)
                elif sys.platform.startswith('darwin'):
                    os.system(f"open {tmp_file.name}")
                else:
                    os.system(f"xdg-open {tmp_file.name}")

                messagebox.showinfo("Info", "ID card opened in default application for printing")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to print ID card: {str(e)}")

    def export_all_cards(self):
        """Export all ID cards"""
        if self.current_dataset is None or self.current_template is None:
            messagebox.showwarning("Warning", "Please activate both dataset and template")
            return

        # Ask user for export directory
        export_dir = filedialog.askdirectory(title="Select Export Directory")
        if not export_dir:
            return

        export_path = Path(export_dir)

        try:
            # Get QR position and size
            qr_x = int(self.qr_x_var.get() or 50)
            qr_y = int(self.qr_y_var.get() or 50)
            qr_size = int(self.qr_size_var.get() or 100)

            exported_count = 0

            for _, employee_data in self.current_dataset.iterrows():
                try:
                    # Create ID card
                    card_img = self.create_id_card_preview(employee_data, qr_x, qr_y, qr_size)

                    if card_img:
                        # Save card
                        filename = f"ID_Card_{employee_data['ID']}_{employee_data['Name'].replace(' ', '_')}.png"
                        card_path = export_path / filename
                        card_img.save(card_path)
                        exported_count += 1

                except Exception as e:
                    print(f"Failed to export card for {employee_data['ID']}: {e}")

            messagebox.showinfo(
                "Export Complete",
                f"Exported {exported_count} ID cards to:\n{export_dir}"
            )

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export cards: {str(e)}")

    def create_email_frame(self):
        """Create the email management frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["email"] = frame

        # Title
        title = ctk.CTkLabel(
            frame,
            text="Email QR Codes",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # Email configuration
        config_frame = ctk.CTkFrame(frame)
        config_frame.pack(fill="x", padx=20, pady=10)

        config_title = ctk.CTkLabel(
            config_frame,
            text="Email Configuration",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        config_title.pack(pady=10)

        # Configuration form
        form_frame = ctk.CTkFrame(config_frame)
        form_frame.pack(fill="x", padx=20, pady=10)
        form_frame.grid_columnconfigure(1, weight=1)

        # SMTP Server
        ctk.CTkLabel(form_frame, text="SMTP Server:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.smtp_server_var = ctk.StringVar(value=self.config["email_config"]["smtp_server"])
        self.smtp_server_entry = ctk.CTkEntry(form_frame, textvariable=self.smtp_server_var)
        self.smtp_server_entry.grid(row=0, column=1, sticky="ew", padx=5, pady=5)

        # SMTP Port
        ctk.CTkLabel(form_frame, text="SMTP Port:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.smtp_port_var = ctk.StringVar(value=str(self.config["email_config"]["smtp_port"]))
        self.smtp_port_entry = ctk.CTkEntry(form_frame, textvariable=self.smtp_port_var, width=100)
        self.smtp_port_entry.grid(row=1, column=1, sticky="w", padx=5, pady=5)

        # Username
        ctk.CTkLabel(form_frame, text="Username:").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        self.email_username_var = ctk.StringVar(value=self.config["email_config"]["username"])
        self.email_username_entry = ctk.CTkEntry(form_frame, textvariable=self.email_username_var)
        self.email_username_entry.grid(row=2, column=1, sticky="ew", padx=5, pady=5)

        # Password
        ctk.CTkLabel(form_frame, text="Password:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        self.email_password_var = ctk.StringVar(value=self.config["email_config"]["password"])
        self.email_password_entry = ctk.CTkEntry(form_frame, textvariable=self.email_password_var, show="*")
        self.email_password_entry.grid(row=3, column=1, sticky="ew", padx=5, pady=5)

        # TLS checkbox
        self.use_tls_var = ctk.BooleanVar(value=self.config["email_config"]["use_tls"])
        self.use_tls_checkbox = ctk.CTkCheckBox(form_frame, text="Use TLS", variable=self.use_tls_var)
        self.use_tls_checkbox.grid(row=4, column=1, sticky="w", padx=5, pady=5)

        # Save config button
        save_config_btn = ctk.CTkButton(
            config_frame,
            text="Save Email Configuration",
            command=self.save_email_config,
            height=40
        )
        save_config_btn.pack(pady=10)

        # Test email button
        test_email_btn = ctk.CTkButton(
            config_frame,
            text="Test Email Connection",
            command=self.test_email_connection,
            height=40
        )
        test_email_btn.pack(pady=5)

        # Email sending section
        sending_frame = ctk.CTkFrame(frame)
        sending_frame.pack(fill="both", expand=True, padx=20, pady=10)

        sending_title = ctk.CTkLabel(
            sending_frame,
            text="Send QR Codes",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        sending_title.pack(pady=10)

        # Employee list with email status
        self.email_tree = ttk.Treeview(
            sending_frame,
            columns=("ID", "Name", "Email", "Status"),
            show="headings",
            height=10
        )

        # Configure columns
        self.email_tree.heading("ID", text="ID")
        self.email_tree.heading("Name", text="Name")
        self.email_tree.heading("Email", text="Email")
        self.email_tree.heading("Status", text="Status")

        self.email_tree.column("ID", width=60)
        self.email_tree.column("Name", width=150)
        self.email_tree.column("Email", width=200)
        self.email_tree.column("Status", width=100)

        self.email_tree.pack(fill="both", expand=True, padx=20, pady=10)

        # Email actions
        email_actions_frame = ctk.CTkFrame(sending_frame)
        email_actions_frame.pack(fill="x", padx=20, pady=10)

        send_selected_btn = ctk.CTkButton(
            email_actions_frame,
            text="Send to Selected",
            command=self.send_selected_emails,
            width=150
        )
        send_selected_btn.pack(side="left", padx=5)

        send_all_btn = ctk.CTkButton(
            email_actions_frame,
            text="Send to All",
            command=self.send_all_emails,
            width=150
        )
        send_all_btn.pack(side="left", padx=5)

        refresh_email_btn = ctk.CTkButton(
            email_actions_frame,
            text="Refresh List",
            command=self.refresh_email_list,
            width=120
        )
        refresh_email_btn.pack(side="right", padx=5)

        # Refresh email list
        self.refresh_email_list()

    def save_email_config(self):
        """Save email configuration"""
        try:
            self.config["email_config"] = {
                "smtp_server": self.smtp_server_var.get(),
                "smtp_port": int(self.smtp_port_var.get()),
                "username": self.email_username_var.get(),
                "password": self.email_password_var.get(),
                "use_tls": self.use_tls_var.get()
            }
            self.save_config()
            messagebox.showinfo("Success", "Email configuration saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save email configuration: {str(e)}")

    def test_email_connection(self):
        """Test email connection"""
        try:
            config = self.config["email_config"]

            if not config["username"] or not config["password"]:
                messagebox.showwarning("Warning", "Please enter username and password")
                return

            # Test connection
            server = smtplib.SMTP(config["smtp_server"], config["smtp_port"])
            if config["use_tls"]:
                server.starttls()
            server.login(config["username"], config["password"])
            server.quit()

            messagebox.showinfo("Success", "Email connection test successful!")

        except Exception as e:
            messagebox.showerror("Error", f"Email connection test failed: {str(e)}")

    def refresh_email_list(self):
        """Refresh email list"""
        # Clear existing items
        for item in self.email_tree.get_children():
            self.email_tree.delete(item)

        if self.current_dataset is None:
            return

        # Add employees with email status
        for _, row in self.current_dataset.iterrows():
            employee_id = str(row['ID'])
            name = str(row['Name'])
            email = str(row.get('Email', '')) if 'Email' in row else ''

            # Check email status
            if not email or email.lower() in ['nan', 'none', '']:
                status = "No Email"
            else:
                status = "Ready"

            self.email_tree.insert("", "end", values=(employee_id, name, email, status))

    def send_selected_emails(self):
        """Send emails to selected employees"""
        selected_items = self.email_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "Please select employees to send emails to")
            return

        # Get selected employee data
        employees = []
        for item in selected_items:
            values = self.email_tree.item(item, "values")
            if values[3] == "Ready":  # Only send to employees with email
                employees.append({
                    'ID': values[0],
                    'Name': values[1],
                    'Email': values[2]
                })

        if not employees:
            messagebox.showwarning("Warning", "No employees with valid email addresses selected")
            return

        self.send_emails_to_employees(employees)

    def send_all_emails(self):
        """Send emails to all employees with email addresses"""
        if self.current_dataset is None:
            messagebox.showwarning("Warning", "No active dataset")
            return

        # Get all employees with email
        employees = []
        for _, row in self.current_dataset.iterrows():
            email = str(row.get('Email', '')) if 'Email' in row else ''
            if email and email.lower() not in ['nan', 'none', '']:
                employees.append({
                    'ID': str(row['ID']),
                    'Name': str(row['Name']),
                    'Email': email
                })

        if not employees:
            messagebox.showwarning("Warning", "No employees with valid email addresses found")
            return

        self.send_emails_to_employees(employees)

    def send_emails_to_employees(self, employees):
        """Send emails to specified employees"""
        config = self.config["email_config"]

        if not config["username"] or not config["password"]:
            messagebox.showwarning("Warning", "Please configure email settings first")
            return

        # Confirm sending
        if not messagebox.askyesno(
            "Confirm Email Sending",
            f"Send QR codes to {len(employees)} employees via email?"
        ):
            return

        # Start sending in background thread
        thread = threading.Thread(
            target=self.email_sending_worker,
            args=(employees, config),
            daemon=True
        )
        thread.start()

    def email_sending_worker(self, employees, config):
        """Background worker for sending emails"""
        try:
            # Connect to SMTP server
            server = smtplib.SMTP(config["smtp_server"], config["smtp_port"])
            if config["use_tls"]:
                server.starttls()
            server.login(config["username"], config["password"])

            sent_count = 0
            failed_count = 0

            for employee in employees:
                try:
                    # Check if QR code exists
                    qr_path = self.qr_folder / f"{employee['ID']}.png"
                    if not qr_path.exists():
                        print(f"QR code not found for {employee['ID']}")
                        failed_count += 1
                        continue

                    # Create email
                    msg = MIMEMultipart()
                    msg['From'] = config["username"]
                    msg['To'] = employee['Email']
                    msg['Subject'] = f"Your QR ID Card - {employee['Name']}"

                    # Email body
                    body = f"""
Hello {employee['Name']},

Your QR ID card has been generated successfully. Please find your QR code attached to this email.

Employee Details:
- ID: {employee['ID']}
- Name: {employee['Name']}

Instructions:
- Save the attached QR code image to your device
- Present this QR code when required for identification
- Keep this email for your records

This is an automated message from the ID Management System.
                    """

                    msg.attach(MIMEText(body, 'plain'))

                    # Attach QR code
                    with open(qr_path, 'rb') as f:
                        img_data = f.read()
                        img = MIMEImage(img_data)
                        img.add_header(
                            'Content-Disposition',
                            f'attachment; filename="QR_ID_{employee["ID"]}_{employee["Name"].replace(" ", "_")}.png"'
                        )
                        msg.attach(img)

                    # Send email
                    server.send_message(msg)
                    sent_count += 1
                    print(f"Email sent to {employee['Email']}")

                    # Brief pause to avoid overwhelming the server
                    time.sleep(1)

                except Exception as e:
                    print(f"Failed to send email to {employee['Email']}: {e}")
                    failed_count += 1

            server.quit()

            # Show results
            self.root.after(0, lambda: messagebox.showinfo(
                "Email Sending Complete",
                f"Emails sent: {sent_count}\nFailed: {failed_count}"
            ))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror(
                "Email Error",
                f"Failed to send emails: {str(e)}"
            ))

    def create_settings_frame(self):
        """Create the settings frame"""
        frame = ctk.CTkFrame(self.main_frame)
        self.content_frames["settings"] = frame

        # Title
        title = ctk.CTkLabel(
            frame,
            text="Settings",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # Application settings
        app_frame = ctk.CTkFrame(frame)
        app_frame.pack(fill="x", padx=20, pady=10)

        app_title = ctk.CTkLabel(
            app_frame,
            text="Application Settings",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        app_title.pack(pady=10)

        # Theme selection
        theme_frame = ctk.CTkFrame(app_frame)
        theme_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(theme_frame, text="Appearance Mode:").pack(side="left", padx=10)

        self.theme_var = ctk.StringVar(value="Light")
        theme_combo = ctk.CTkComboBox(
            theme_frame,
            values=["Light", "Dark", "System"],
            variable=self.theme_var,
            command=self.change_theme
        )
        theme_combo.pack(side="right", padx=10)

        # Data management
        data_frame = ctk.CTkFrame(frame)
        data_frame.pack(fill="x", padx=20, pady=10)

        data_title = ctk.CTkLabel(
            data_frame,
            text="Data Management",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        data_title.pack(pady=10)

        # Cleanup buttons
        cleanup_frame = ctk.CTkFrame(data_frame)
        cleanup_frame.pack(fill="x", padx=20, pady=10)

        cleanup_qr_btn = ctk.CTkButton(
            cleanup_frame,
            text="Clean QR Codes",
            command=self.cleanup_qr_codes,
            width=150
        )
        cleanup_qr_btn.pack(side="left", padx=5)

        cleanup_all_btn = ctk.CTkButton(
            cleanup_frame,
            text="Clean All Data",
            command=self.cleanup_all_data,
            width=150,
            fg_color="red"
        )
        cleanup_all_btn.pack(side="right", padx=5)

        # About section
        about_frame = ctk.CTkFrame(frame)
        about_frame.pack(fill="x", padx=20, pady=10)

        about_title = ctk.CTkLabel(
            about_frame,
            text="About",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        about_title.pack(pady=10)

        about_text = ctk.CTkLabel(
            about_frame,
            text="Smart QR ID Scanner - Desktop Application\nVersion 1.0\n\nA local desktop application for QR code ID management and printing.\nWorks offline except for email functionality.",
            font=ctk.CTkFont(size=12),
            justify="center"
        )
        about_text.pack(pady=10)

    def change_theme(self, theme):
        """Change application theme"""
        ctk.set_appearance_mode(theme.lower())

    def cleanup_qr_codes(self):
        """Clean up QR codes"""
        if not messagebox.askyesno("Confirm Cleanup", "Delete all generated QR codes?"):
            return

        try:
            count = 0
            for qr_file in self.qr_folder.glob("*.png"):
                qr_file.unlink()
                count += 1

            messagebox.showinfo("Cleanup Complete", f"Deleted {count} QR code files")
            self.refresh_employee_list()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to cleanup QR codes: {str(e)}")

    def cleanup_all_data(self):
        """Clean up all application data"""
        if not messagebox.askyesno(
            "Confirm Cleanup",
            "Delete ALL data including datasets, templates, and QR codes?\nThis action cannot be undone!"
        ):
            return

        try:
            # Clean QR codes
            qr_count = len(list(self.qr_folder.glob("*.png")))
            for qr_file in self.qr_folder.glob("*.png"):
                qr_file.unlink()

            # Clean datasets
            dataset_count = len(list(self.dataset_dir.glob("*.csv")))
            for dataset_file in self.dataset_dir.glob("*.csv"):
                dataset_file.unlink()

            # Clean templates
            template_count = 0
            for ext in ['*.png', '*.jpg', '*.jpeg', '*.gif', '*.bmp', '*.webp', '*.tiff', '*.tif']:
                for template_file in self.template_dir.glob(ext):
                    template_file.unlink()
                    template_count += 1

            # Reset configuration
            self.config = {
                "active_dataset": None,
                "active_template": None,
                "email_config": {
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "username": "",
                    "password": "",
                    "use_tls": True
                }
            }
            self.save_config()

            # Reset current data
            self.current_dataset = None
            self.current_template = None

            # Refresh all UI
            self.refresh_data()
            self.refresh_dataset_list()
            self.refresh_template_list()
            self.refresh_employee_list()
            self.refresh_email_list()

            messagebox.showinfo(
                "Cleanup Complete",
                f"Deleted:\n- {qr_count} QR codes\n- {dataset_count} datasets\n- {template_count} templates"
            )

        except Exception as e:
            messagebox.showerror("Error", f"Failed to cleanup data: {str(e)}")

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = QRIDDesktopApp()
    app.run()
