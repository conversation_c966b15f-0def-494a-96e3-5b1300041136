#!/usr/bin/env python3
"""
Smart QR ID Scanner - Desktop Application Launcher
Simple launcher script that handles dependencies and starts the application.
"""

import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def install_dependencies():
    print("Installing dependencies...")

    try:
        subprocess.check_call([
            "py", "-3.13", "-m", "pip", "install", "-r", "requirements_desktop.txt"
        ])
    except subprocess.CalledProcessError:
        print("Python 3.13 not found or failed — falling back to current Python version...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements_desktop.txt"
            ])
        except subprocess.CalledProcessError as e:
            print(f"Failed to install dependencies even with fallback: {e}")
            return False
    print("Dependencies installed successfully!")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'customtkinter',
        'pandas',
        'qrcode',
        'Pillow',
        'cryptography',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def create_directories():
    """Create necessary directories"""
    base_dir = Path(__file__).parent
    directories = [
        base_dir / "data",
        base_dir / "data" / "qr_codes",
        base_dir / "data" / "participant_list",
        base_dir / "data" / "id_templates"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    print("Directories created successfully!")

def generate_encryption_key():
    """Generate encryption key if it doesn't exist"""
    key_file = Path(__file__).parent / "qr_key.key"
    
    if not key_file.exists():
        try:
            from cryptography.fernet import Fernet
            key = Fernet.generate_key()
            with open(key_file, "wb") as f:
                f.write(key)
            print("Encryption key generated successfully!")
        except ImportError:
            print("Warning: Could not generate encryption key. Please install dependencies first.")

def main():
    """Main launcher function"""
    print("Smart QR ID Scanner - Desktop Application")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Check dependencies
    missing = check_dependencies()
    if missing:
        print(f"Missing dependencies: {', '.join(missing)}")
        
        # Ask user if they want to install
        response = input("Would you like to install missing dependencies? (y/n): ").lower()
        if response in ['y', 'yes']:
            if not install_dependencies():
                input("Press Enter to exit...")
                return
        else:
            print("Cannot start application without required dependencies.")
            input("Press Enter to exit...")
            return
    
    # Create directories
    create_directories()
    
    # Generate encryption key
    generate_encryption_key()
    
    # Start the application
    print("Starting application...")
    try:
        from desktop_app import QRIDDesktopApp
        app = QRIDDesktopApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
